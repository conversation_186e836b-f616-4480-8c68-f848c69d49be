# 数据仓库数据地图

一个酷炫的数据仓库数据地图可视化界面，展示业务域、数据流向、热门数据表等信息。

## 功能特性

### 🎨 视觉效果
- **粒子背景动画** - 科技感十足的粒子流动效果
- **渐变色彩** - 现代化的蓝色到红色渐变主题
- **悬停动效** - 卡片悬停放大、发光效果
- **加载动画** - 优雅的数据加载动效
- **响应式设计** - 适配不同屏幕尺寸

### 📊 数据展示
- **业务域概览** - 8个主要业务域的可视化展示
- **数据流图** - 展示ODS、DWD、DWS、DIM、ADS各层数据流向
- **热门数据表** - 显示最常用的数据表信息
- **数据质量监控** - 实时展示数据完整性、时效性、准确性指标

### 🔍 交互功能
- **智能搜索** - 支持搜索业务域、数据表、字段
- **搜索建议** - 实时显示搜索建议
- **详情查看** - 点击卡片查看详细信息
- **模态框展示** - 优雅的弹窗展示详细内容

## 技术栈

- **HTML5** - 语义化标记
- **CSS3** - 现代CSS特性，包括Grid、Flexbox、动画
- **JavaScript ES6+** - 原生JavaScript，无框架依赖
- **SVG** - 数据流图绘制
- **Font Awesome** - 图标库

## 文件结构

```
├── index.html          # 主页面
├── css/
│   └── style.css      # 样式文件
├── js/
│   ├── data.js        # 数据配置
│   └── main.js        # 主要逻辑
└── README.md          # 说明文档
```

## 快速开始

1. 直接在浏览器中打开 `index.html` 文件
2. 或者使用本地服务器：
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 使用Node.js
   npx serve .
   ```

## 数据配置

所有数据配置都在 `js/data.js` 文件中，包括：

- **业务域配置** - 域名、描述、统计信息
- **数据表配置** - 表名、描述、使用频率
- **数据流配置** - 节点和连接关系
- **搜索配置** - 搜索建议和结果类型

## 自定义配置

### 修改业务域

在 `js/data.js` 中的 `DATA_CONFIG.domains` 数组中添加或修改业务域：

```javascript
{
    id: 'your-domain',
    name: '你的业务域',
    icon: 'fas fa-your-icon',
    description: '业务域描述',
    tableCount: 50,
    fieldCount: 300,
    updateFreq: '实时',
    color: '#your-color'
}
```

### 修改数据流

在 `DATA_CONFIG.dataFlow` 中配置节点和连接：

```javascript
dataFlow: {
    nodes: [
        { id: 'node1', name: '节点1', x: 100, y: 200, type: 'source' }
    ],
    links: [
        { source: 'node1', target: 'node2', label: '数据流' }
    ]
}
```

## 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
