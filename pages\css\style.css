/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
    color: #212529;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 粒子背景 */
#particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(108, 117, 125, 0.4);
    border-radius: 50%;
    animation: float 6s infinite linear;
}

@keyframes float {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* 主容器 */
.main-container {
    position: relative;
    z-index: 1;
    min-height: 100vh;
}

/* 顶部导航 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(108, 117, 125, 0.2);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 2rem;
    color: #495057;
    animation: pulse 2s infinite;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(45deg, #495057, #6c757d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 搜索框 */
.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

#search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 1rem;
    background: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.3);
    border-radius: 25px;
    color: #212529;
    font-size: 1rem;
    transition: all 0.3s ease;
}

#search-input:focus {
    outline: none;
    border-color: #495057;
    box-shadow: 0 0 20px rgba(108, 117, 125, 0.2);
    background: #ffffff;
}

#search-input::placeholder {
    color: rgba(108, 117, 125, 0.6);
}

.search-container i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #495057;
}

/* 统计信息 */
.header-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
    animation: countUp 2s ease-out;
}

.stat-label {
    font-size: 0.8rem;
    color: rgba(108, 117, 125, 0.8);
}

@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 主要内容 */
.main-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #212529;
}

.section-title i {
    color: #495057;
}

/* 数据资产分类导航 */
.asset-categories {
    margin-bottom: 3rem;
}

.categories-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.category-item {
    background: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.2);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #495057;
}

.category-item.active {
    background: #f8f9fa;
    border-color: #495057;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.category-icon {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    color: #495057;
}

.category-name {
    font-size: 0.9rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.3rem;
}

.category-count {
    font-size: 0.8rem;
    color: rgba(108, 117, 125, 0.8);
}

/* 业务域网格 */
.domains-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.domain-card {
    background: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.domain-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s;
}

.domain-card:hover::before {
    left: 100%;
}

.domain-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-color: #495057;
}

.domain-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.domain-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #212529;
}

.domain-icon {
    font-size: 1.5rem;
    color: #495057;
}

.domain-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.domain-stat {
    text-align: center;
    background: rgba(248, 249, 250, 0.8);
    padding: 0.5rem;
    border-radius: 8px;
}

.domain-stat-number {
    display: block;
    font-size: 1.1rem;
    font-weight: bold;
    color: #495057;
}

.domain-stat-label {
    font-size: 0.7rem;
    color: rgba(108, 117, 125, 0.8);
    margin-top: 0.2rem;
}

.domain-asset-types {
    margin-top: 1rem;
}

.asset-type-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
}

.asset-type-tag {
    background: rgba(248, 249, 250, 0.8);
    color: #495057;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.domain-description {
    color: rgba(108, 117, 125, 0.9);
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 数据流图 */
.data-flow-section {
    margin-bottom: 3rem;
}

.lineage-container {
    background: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.2);
    border-radius: 15px;
    padding: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#lineage-svg {
    width: 100%;
    height: 400px;
}

.flow-node {
    fill: rgba(248, 249, 250, 0.8);
    stroke: #495057;
    stroke-width: 2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.flow-node:hover {
    fill: rgba(233, 236, 239, 0.9);
    stroke-width: 3;
}

.flow-link {
    stroke: rgba(108, 117, 125, 0.6);
    stroke-width: 2;
    fill: none;
    animation: flowAnimation 3s infinite linear;
}

@keyframes flowAnimation {
    0% { stroke-dasharray: 5, 5; stroke-dashoffset: 0; }
    100% { stroke-dasharray: 5, 5; stroke-dashoffset: 10; }
}

.flow-text {
    fill: #212529;
    font-size: 12px;
    text-anchor: middle;
    dominant-baseline: middle;
}

/* 热门数据资产 */
.assets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
    margin-bottom: 3rem;
}

.asset-card {
    background: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.2);
    border-radius: 12px;
    padding: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.asset-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.05), transparent);
    transition: left 0.5s;
}

.asset-card:hover::before {
    left: 100%;
}

.asset-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    background: #f8f9fa;
    border-color: #495057;
}

.asset-header {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
}

.asset-icon {
    font-size: 1.2rem;
    color: #495057;
}

.asset-type {
    background: rgba(248, 249, 250, 0.8);
    color: #495057;
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-left: auto;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.asset-name {
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.asset-description {
    color: rgba(108, 117, 125, 0.8);
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
    line-height: 1.4;
}

.asset-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: rgba(108, 117, 125, 0.7);
}

.asset-domain {
    color: #495057;
    font-weight: 500;
}

.asset-usage {
    padding: 0.2rem 0.5rem;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
}

.asset-usage.high {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.asset-usage.medium {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.asset-usage.low {
    background: rgba(25, 135, 84, 0.1);
    color: #198754;
}

/* 数据质量监控 */
.quality-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.quality-card {
    background: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.quality-icon {
    font-size: 2rem;
    color: #495057;
}

.quality-info h3 {
    margin-bottom: 0.5rem;
    color: #212529;
}

.progress-bar {
    width: 200px;
    height: 8px;
    background: rgba(233, 236, 239, 0.8);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #495057, #6c757d);
    border-radius: 4px;
    width: 0;
    animation: progressAnimation 2s ease-out forwards;
}

@keyframes progressAnimation {
    to { width: var(--progress); }
}

.progress-text {
    font-weight: bold;
    color: #495057;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: #ffffff;
    margin: 5% auto;
    padding: 2rem;
    border: 1px solid rgba(108, 117, 125, 0.3);
    border-radius: 15px;
    width: 80%;
    max-width: 800px;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

@keyframes modalSlideIn {
    from { opacity: 0; transform: translateY(-50px); }
    to { opacity: 1; transform: translateY(0); }
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: #495057;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(108, 117, 125, 0.3);
    border-top: 3px solid #495057;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 额外动画效果 */
.glow-effect {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(64, 224, 255, 0.2);
    }
    to {
        box-shadow: 0 0 20px rgba(64, 224, 255, 0.6), 0 0 30px rgba(64, 224, 255, 0.4);
    }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 数据流动画增强 */
.flow-particle {
    fill: #40e0ff;
    r: 3;
    animation: flowParticle 4s infinite linear;
}

@keyframes flowParticle {
    0% { opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0; }
}

/* 悬停效果增强 */
.domain-card:hover .domain-icon {
    animation: iconSpin 0.5s ease-in-out;
}

@keyframes iconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.table-card:hover {
    background: linear-gradient(135deg, rgba(64, 224, 255, 0.1) 0%, rgba(255, 107, 107, 0.1) 100%);
}

/* 搜索建议框 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    border: 1px solid rgba(108, 117, 125, 0.3);
    border-radius: 10px;
    margin-top: 5px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.suggestion-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background 0.2s ease;
    border-bottom: 1px solid rgba(108, 117, 125, 0.1);
    color: #212529;
}

.suggestion-item:hover {
    background: rgba(248, 249, 250, 0.8);
}

.suggestion-item:last-child {
    border-bottom: none;
}

/* 模态框内容样式 */
.domain-detail-stats {
    display: flex;
    justify-content: space-around;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 10px;
}

.domain-detail-stats .stat-item {
    text-align: center;
}

.domain-detail-stats .stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

.domain-detail-stats .stat-label {
    font-size: 0.9rem;
    color: rgba(108, 117, 125, 0.8);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(233, 236, 239, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #495057, #6c757d);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #6c757d, #495057);
}

/* 资产详情样式 */
.asset-detail-header {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.asset-type-badge, .asset-domain-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.asset-type-badge {
    background: rgba(248, 249, 250, 0.8);
    color: #495057;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.asset-domain-badge {
    background: rgba(233, 236, 239, 0.8);
    color: #495057;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.asset-detail-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 10px;
}

.usage-high {
    color: #dc3545 !important;
}

.usage-medium {
    color: #ffc107 !important;
}

.usage-low {
    color: #198754 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-stats {
        gap: 1rem;
        flex-wrap: wrap;
    }

    .categories-nav {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .main-content {
        padding: 1rem;
    }

    .domains-grid {
        grid-template-columns: 1fr;
    }

    .assets-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 1rem;
    }

    .quality-dashboard {
        grid-template-columns: 1fr;
    }

    .domain-detail-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .asset-detail-stats {
        grid-template-columns: 1fr;
    }

    #lineage-svg {
        height: 300px;
    }
}
