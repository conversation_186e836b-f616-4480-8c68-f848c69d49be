// 数据资产配置数据
const DATA_CONFIG = {
    // 数据资产分类
    assetCategories: [
        {
            id: 'database',
            name: '数据库资产',
            icon: 'fas fa-database',
            description: '关系型数据库、NoSQL数据库、数据仓库等结构化数据资产',
            color: '#40e0ff',
            count: 298
        },
        {
            id: 'file',
            name: '文件资产',
            icon: 'fas fa-file-alt',
            description: 'CSV、JSON、XML、Excel等文件格式数据资产',
            color: '#4ecdc4',
            count: 156
        },
        {
            id: 'api',
            name: 'API资产',
            icon: 'fas fa-plug',
            description: 'REST API、GraphQL、WebSocket等接口数据资产',
            color: '#ff6b6b',
            count: 89
        },
        {
            id: 'stream',
            name: '流数据资产',
            icon: 'fas fa-stream',
            description: 'Kafka、消息队列、实时数据流等流式数据资产',
            color: '#f7b731',
            count: 45
        },
        {
            id: 'report',
            name: '报表资产',
            icon: 'fas fa-chart-bar',
            description: '业务报表、分析报告、仪表板等可视化资产',
            color: '#5f27cd',
            count: 234
        },
        {
            id: 'model',
            name: '模型资产',
            icon: 'fas fa-brain',
            description: '机器学习模型、数据模型、算法模型等智能资产',
            color: '#ff9ff3',
            count: 67
        }
    ],

    // 数据资产域配置
    domains: [
        {
            id: 'cust',
            name: '客户域',
            icon: 'fas fa-users',
            description: '客户基础信息、客户关系管理、客户画像分析等核心客户数据资产',
            assetCount: 78,
            apiCount: 12,
            reportCount: 23,
            updateFreq: '实时',
            color: '#40e0ff',
            subDomains: ['客户基础信息', '客户关系', '客户画像', '客户行为'],
            assetTypes: ['数据库表', 'API接口', '客户报表', '画像模型']
        },
        {
            id: 'sales',
            name: '销售域',
            icon: 'fas fa-chart-line',
            description: '销售过程管理、商机跟踪、合同管理、项目实施等销售全流程数据资产',
            assetCount: 95,
            apiCount: 18,
            reportCount: 34,
            updateFreq: '每小时',
            color: '#ff6b6b',
            subDomains: ['商机管理', '合同管理', '项目实施', '销售分析'],
            assetTypes: ['数据库表', 'CRM接口', '销售报表', '预测模型']
        },
        {
            id: 'fin',
            name: '财务域',
            icon: 'fas fa-coins',
            description: '财务核算、资金管理、成本控制、财务分析等财务管理数据资产',
            assetCount: 72,
            apiCount: 8,
            reportCount: 28,
            updateFreq: '每日',
            color: '#4ecdc4',
            subDomains: ['财务核算', '资金管理', '成本分析', '预算管理'],
            assetTypes: ['财务数据表', '支付接口', '财务报表', '风控模型']
        },
        {
            id: 'oprn',
            name: '运营域',
            icon: 'fas fa-cogs',
            description: '运营活动管理、营销分析、用户运营、产品运营等运营数据资产',
            assetCount: 56,
            apiCount: 15,
            reportCount: 19,
            updateFreq: '每小时',
            color: '#45b7d1',
            subDomains: ['营销运营', '用户运营', '产品运营', '数据运营'],
            assetTypes: ['运营数据表', '营销接口', '运营报表', '推荐模型']
        },
        {
            id: 'hr',
            name: '人事域',
            icon: 'fas fa-user-tie',
            description: '人员组织架构、绩效考核、薪酬管理、人才发展等人力资源数据资产',
            assetCount: 42,
            apiCount: 6,
            reportCount: 16,
            updateFreq: '每日',
            color: '#f7b731',
            subDomains: ['组织架构', '绩效管理', '薪酬福利', '人才发展'],
            assetTypes: ['HR数据表', '人事接口', 'HR报表', '人才模型']
        },
        {
            id: 'ts',
            name: '技术支持域',
            icon: 'fas fa-headset',
            description: '客户服务、技术支持、问题跟踪、服务质量等客服技术数据资产',
            assetCount: 48,
            apiCount: 11,
            reportCount: 14,
            updateFreq: '实时',
            color: '#5f27cd',
            subDomains: ['客户服务', '技术支持', '问题管理', '服务质量'],
            assetTypes: ['工单数据表', '服务接口', '服务报表', '智能客服']
        },
        {
            id: 'mkt',
            name: '市场域',
            icon: 'fas fa-bullhorn',
            description: '市场活动、品牌推广、渠道管理、市场分析等市场营销数据资产',
            assetCount: 35,
            apiCount: 9,
            reportCount: 21,
            updateFreq: '每日',
            color: '#ff9ff3',
            subDomains: ['市场活动', '品牌推广', '渠道管理', '竞品分析'],
            assetTypes: ['营销数据表', '广告接口', '市场报表', '营销模型']
        },
        {
            id: 'pub',
            name: '公共数据域',
            icon: 'fas fa-database',
            description: '字典信息、地理信息、日期维度、财务科目等公共基础数据资产',
            assetCount: 28,
            apiCount: 5,
            reportCount: 8,
            updateFreq: '按需',
            color: '#54a0ff',
            subDomains: ['字典维度', '地理信息', '时间维度', '基础配置'],
            assetTypes: ['维度表', '基础接口', '配置文件', '元数据']
        },
        {
            id: 'external',
            name: '外部数据域',
            icon: 'fas fa-cloud-download-alt',
            description: '第三方数据源、开放数据、合作伙伴数据等外部数据资产',
            assetCount: 67,
            apiCount: 23,
            reportCount: 12,
            updateFreq: '实时',
            color: '#26de81',
            subDomains: ['第三方API', '开放数据', '合作数据', '爬虫数据'],
            assetTypes: ['外部接口', '数据文件', '实时流', '爬虫数据']
        }
    ],

    // 热门数据资产
    popularAssets: [
        {
            name: 'dim_cust_customter_company_df',
            domain: '客户域',
            type: '数据库表',
            description: '客户公司基础信息维度表',
            recordCount: '125,847',
            lastUpdate: '2小时前',
            usage: 'high',
            icon: 'fas fa-table'
        },
        {
            name: 'ads_sales_proj_fin_total_df',
            domain: '销售域',
            type: '数据库表',
            description: '销售项目财务汇总表',
            recordCount: '89,234',
            lastUpdate: '1小时前',
            usage: 'high',
            icon: 'fas fa-table'
        },
        {
            name: 'dwd_fin_pay_apply_detail_df',
            domain: '财务域',
            type: '数据库表',
            description: '付款申请明细数据表',
            recordCount: '234,567',
            lastUpdate: '30分钟前',
            usage: 'medium',
            icon: 'fas fa-table'
        },
        {
            name: 'customer_profile_api',
            domain: '客户域',
            type: 'API接口',
            description: '客户画像数据API接口',
            recordCount: '实时',
            lastUpdate: '5分钟前',
            usage: 'high',
            icon: 'fas fa-plug'
        },
        {
            name: 'sales_dashboard',
            domain: '销售域',
            type: '仪表板',
            description: '销售业绩实时监控仪表板',
            recordCount: '实时',
            lastUpdate: '实时',
            usage: 'high',
            icon: 'fas fa-chart-bar'
        },
        {
            name: 'marketing_model_v2',
            domain: '市场域',
            type: '机器学习模型',
            description: '营销效果预测模型V2.0',
            recordCount: '模型',
            lastUpdate: '1天前',
            usage: 'medium',
            icon: 'fas fa-brain'
        },
        {
            name: 'external_weather_stream',
            domain: '外部数据域',
            type: '数据流',
            description: '第三方天气数据实时流',
            recordCount: '流式',
            lastUpdate: '实时',
            usage: 'medium',
            icon: 'fas fa-stream'
        },
        {
            name: 'hr_report_monthly',
            domain: '人事域',
            type: '报表',
            description: '人力资源月度分析报表',
            recordCount: '报表',
            lastUpdate: '2天前',
            usage: 'low',
            icon: 'fas fa-file-alt'
        }
    ],

    // 数据血缘关系配置
    dataLineage: {
        nodes: [
            { id: 'source_db', name: '源数据库', x: 100, y: 200, type: 'database' },
            { id: 'api_gateway', name: 'API网关', x: 300, y: 100, type: 'api' },
            { id: 'data_lake', name: '数据湖', x: 300, y: 200, type: 'storage' },
            { id: 'stream_proc', name: '流处理', x: 300, y: 300, type: 'stream' },
            { id: 'ml_model', name: 'ML模型', x: 500, y: 150, type: 'model' },
            { id: 'dashboard', name: '仪表板', x: 500, y: 250, type: 'visualization' },
            { id: 'report', name: '业务报表', x: 700, y: 100, type: 'report' },
            { id: 'app', name: '业务应用', x: 700, y: 300, type: 'application' }
        ],
        links: [
            { source: 'source_db', target: 'api_gateway', label: 'API调用' },
            { source: 'source_db', target: 'data_lake', label: '数据同步' },
            { source: 'source_db', target: 'stream_proc', label: '实时流' },
            { source: 'data_lake', target: 'ml_model', label: '模型训练' },
            { source: 'api_gateway', target: 'dashboard', label: '实时展示' },
            { source: 'stream_proc', target: 'dashboard', label: '流式展示' },
            { source: 'ml_model', target: 'report', label: '智能分析' },
            { source: 'dashboard', target: 'app', label: '数据服务' }
        ]
    },

    // 数据质量指标
    qualityMetrics: {
        completeness: 95,
        timeliness: 88,
        accuracy: 92,
        consistency: 90
    },

    // 统计信息
    statistics: {
        totalAssets: 889,
        totalDomains: 9,
        totalApis: 97,
        totalReports: 155,
        totalRecords: '1,234,567,890',
        dailyUpdates: '2,345,678'
    }
};

// 搜索配置
const SEARCH_CONFIG = {
    // 搜索建议
    suggestions: [
        '客户基础信息',
        '销售合同',
        '财务付款',
        '运营活动',
        '人员组织',
        '技术支持',
        '市场推广',
        '外部数据',
        'API接口',
        '机器学习模型',
        '实时仪表板',
        'dim_cust',
        'ads_sales',
        'dwd_fin'
    ],

    // 搜索结果类型
    resultTypes: {
        table: '数据表',
        api: 'API接口',
        report: '报表',
        dashboard: '仪表板',
        model: '机器学习模型',
        stream: '数据流',
        file: '数据文件',
        domain: '资产域'
    }
};

// 主题配置
const THEME_CONFIG = {
    colors: {
        primary: '#495057',
        secondary: '#6c757d',
        accent: '#adb5bd',
        background: '#f8f9fa',
        surface: '#ffffff',
        text: '#212529',
        textSecondary: 'rgba(108, 117, 125, 0.8)'
    },

    animations: {
        duration: {
            fast: '0.2s',
            normal: '0.3s',
            slow: '0.5s'
        },
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DATA_CONFIG,
        SEARCH_CONFIG,
        THEME_CONFIG
    };
}
